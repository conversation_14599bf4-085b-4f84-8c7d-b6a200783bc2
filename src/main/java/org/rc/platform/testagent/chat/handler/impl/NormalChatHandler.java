package org.rc.platform.testagent.chat.handler.impl;

import lombok.extern.slf4j.Slf4j;
import org.rc.platform.testagent.chat.dto.ChatAddReqDTO;
import org.rc.platform.testagent.chat.dto.ChatHandleDTO;
import org.rc.platform.testagent.chat.dto.LlmChatReqBean;
import org.rc.platform.testagent.chat.entity.ChatContent;
import org.rc.platform.testagent.chat.entity.ChatConversation;
import org.rc.platform.testagent.chat.handler.AbstractChatHandler;
import org.rc.platform.testagent.chat.listener.ChatEventSourceListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 普通对话处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class NormalChatHandler extends AbstractChatHandler {

    @Override
    public int order() {
        return 300;
    }

    @Override
    public boolean execute(ChatHandleDTO handleDTO) {
        // 普通对话意图才执行
        return "NORMAL_CHAT".equals(handleDTO.getIntentionCode());
    }

    @Override
    protected boolean doRun(ChatHandleDTO handleDTO) {
        log.info("进入普通对话处理器");

        // 1. 处理会话信息
        Long conversationId = handleConversation(handleDTO.getReqDTO());
        handleDTO.setConversationId(conversationId);

        // 2. 保存用户输入内容
        Long contentId = saveUserInput(handleDTO.getReqDTO(), conversationId);
        handleDTO.setContentId(contentId);

        // 3. 构建大模型请求
        LlmChatReqBean llmRequest = buildLlmRequest(handleDTO);

        // 4. 创建监听器（使用 SpringContextUtil 自动获取服务），异步调用大模型
        ChatEventSourceListener listener = new ChatEventSourceListener(handleDTO);
        llmService.callLlmAsync(llmRequest, listener);

        log.info("普通对话处理完成，conversationId: {}, contentId: {}", conversationId, contentId);

        // 标记处理完成，不再执行后续处理器
        handleDTO.markCompleted();
        return false;
    }

    /**
     * 处理会话信息
     */
    private Long handleConversation(ChatAddReqDTO request) {
        if (request.getConversationId() != null && !request.getConversationId().trim().isEmpty()) {
            // 更新现有会话
            try {
                Long conversationId = Long.parseLong(request.getConversationId());
                ChatConversation conversation = chatConversationService.getById(conversationId);
                if (conversation != null) {
                    conversation.setUpdateTime(new Date());
                    chatConversationService.updateById(conversation);
                    return conversationId;
                }
            } catch (NumberFormatException e) {
                log.warn("会话ID格式错误: {}", request.getConversationId());
            }
        }
        
        // 创建新会话
        return createNewConversation(request);
    }

    /**
     * 创建新会话
     */
    private Long createNewConversation(ChatAddReqDTO request) {
        ChatConversation conversation = new ChatConversation();
        
        // 生成会话标题（取用户输入的前20个字符）
        String title = request.getDialogueInput().getDialogue();
        if (title.length() > 20) {
            title = title.substring(0, 20) + "...";
        }
        conversation.setTitle(title);
        // 设置其他默认值
        conversation.setStatus(0); // 正常状态
        conversation.setIsDeleted(0); // 未删除
        conversation.setTenantId(0L); // 默认租户
        Long userIdLong = request.getUserId() != null ? Long.parseLong(request.getUserId()) : 1222477271620092911L;
        conversation.setCreateUser(userIdLong);
        conversation.setUpdateUser(userIdLong);
        conversation.setCreateTime(new Date());
        conversation.setUpdateTime(new Date());
        conversation.setVersion(0);
        chatConversationService.save(conversation);
        log.info("创建新会话成功，conversationId: {}, title: {}", conversation.getId(), title);
        return conversation.getId();
    }

    /**
     * 保存用户输入内容
     */
    private Long saveUserInput(ChatAddReqDTO request, Long conversationId) {
        ChatContent content = new ChatContent();
        
        // 设置会话关联
        content.setConversationId(conversationId);
        
        // 设置必填字段的默认值
        content.setUserId(request.getUserId() != null ? Long.parseLong(request.getUserId()) : 1222477271620092911L);
        content.setAppId(request.getAppId() != null ? Long.parseLong(request.getAppId()) : 1L);
        content.setModelType(request.getModelType() != null ? request.getModelType() : "default");
        
        // 设置用户输入
        content.setInContent(request.getDialogueInput().getDialogue());
        content.setInContentTime(new Date());
        
        // 设置提示词（如果有）
        if (request.getDialogueInput().getPrompt() != null) {
            content.setPrompt(request.getDialogueInput().getPrompt());
        }
        
        // 设置其他默认值
        content.setCommandType(0); // 默认命令类型
        content.setToolsCommand(0); // 默认工具指令
        content.setIsCommand("0"); // 默认非命令
        content.setCommandContent(""); // 默认空字符串
        content.setUserConfirmation("0"); // 默认未确认
        content.setOutContentType(0); // 默认普通文本
        content.setModelTextSize(0); // 初始为0
        content.setModelInputTokens(0); // 初始为0
        content.setModelOutputTokens(0); // 初始为0
        content.setStatus(0); // 正常状态
        content.setIsDeleted(0); // 未删除
        content.setTenantId(0L); // 默认租户
        Long userIdLong = request.getUserId() != null ? Long.parseLong(request.getUserId()) : 1222477271620092911L;
        content.setCreateUser(userIdLong);
        content.setUpdateUser(userIdLong);
        content.setCreateTime(new Date());
        content.setUpdateTime(new Date());
        content.setVersion(0);
        content.setAttribute77("");
        
        chatContentService.save(content);
        log.info("保存用户输入成功，contentId: {}", content.getId());
        
        return content.getId();
    }

    /**
     * 构建大模型请求
     */
    private LlmChatReqBean buildLlmRequest(ChatHandleDTO handleDTO) {
        ChatAddReqDTO request = handleDTO.getReqDTO();
        LlmChatReqBean llmRequest = new LlmChatReqBean();
        llmRequest.setUserId(request.getUserId() != null ? request.getUserId() : "1222477271620092911");
        llmRequest.setModel(request.getModelType() != null ? request.getModelType() : "default");
        llmRequest.setEnableThinking(true);
        llmRequest.setStream(true);
        List<LlmChatReqBean.MessageDTO> messages = new ArrayList<>();

        // 添加系统提示词（如果有）
        if (StringUtils.hasText(handleDTO.getSystemPrompt())) {
            LlmChatReqBean.MessageDTO systemMessage = new LlmChatReqBean.MessageDTO();
            systemMessage.setRole("system");
            systemMessage.setContent(handleDTO.getSystemPrompt());
            messages.add(systemMessage);
            log.info("添加系统提示词，content长度: {}", handleDTO.getSystemPrompt().length());
        }

        // 添加用户消息
        LlmChatReqBean.MessageDTO userMessage = new LlmChatReqBean.MessageDTO();
        userMessage.setRole("user");
        userMessage.setContent(request.getDialogueInput().getDialogue());
        messages.add(userMessage);
        llmRequest.setMessages(messages);
        return llmRequest;
    }


}
